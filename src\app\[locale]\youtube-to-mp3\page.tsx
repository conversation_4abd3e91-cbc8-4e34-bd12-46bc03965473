import { <PERSON><PERSON> } from "@/components/header";
import { Footer } from "@/components/footer";
import { SearchBarWrapper } from "@/components/search-bar-wrapper";
import { BannerAdBeforeSearchBar } from "@/components/ads/banner-ad-before-search-bar";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Check, Download, Music, Headphones, Shield, Zap } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useTranslations } from 'next-intl';
import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import Script from "next/script";
import { generateMp3PageSchemas } from "@/lib/structured-data";


export async function generateMetadata({ params }: { params: { locale: string } }): Promise<Metadata> {
  // Get translations with namespace
  const t = await getTranslations({ locale: params.locale, namespace: 'ytToMp3.metadata' });

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: t('title'),
      description: t('description'),
    }
  };
}

export default function YouTubeToMP3Page() {
  const t = useTranslations('ytToMp3');
  const layout = useTranslations('layout');
  
  // Extract FAQ items for structured data
  const faqItems = [
    { 
      question: t('faq.q1.question'), 
      answer: t('faq.q1.answer') 
    },
    { 
      question: t('faq.q2.question'), 
      answer: t('faq.q2.answer') 
    },
    { 
      question: t('faq.q3.question'), 
      answer: t('faq.q3.answer') 
    },
    { 
      question: t('faq.q4.question'), 
      answer: t('faq.q4.answer') 
    }
  ];
  
  const jsonLdData = generateMp3PageSchemas(
    t('metadata.title'),
    t('metadata.description'),
    layout('metadata.publisher'),
    faqItems
  );
  
  return (
    <>
    <Script
      id="json-ld"
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLdData) }}
      strategy="afterInteractive"
    />
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        {/* Hero Section */}
        <section className="text-center mb-10">
          <h1 className="text-4xl font-bold mb-4">{t('hero.title')}</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto mb-6">
            {t('hero.description')}
          </p>
        </section>

        <BannerAdBeforeSearchBar />

        {/* Search Bar */}
        <section className="mb-12">
          <SearchBarWrapper />

        </section>
        
        {/* Quality Options */}
        <section className="my-12">
          <h2 className="text-2xl font-bold mb-6 text-center">{t('quality.title')}</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader>
                <Badge className="w-fit mb-1">{t('quality.premium.badge')}</Badge>
                <CardTitle>{t('quality.premium.title')}</CardTitle>
                <CardDescription>{t('quality.premium.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.premium.feature1')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.premium.feature2')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.premium.feature3')}</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button className="w-full">
                  {t('quality.premium.button')}
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader>
                <Badge className="w-fit mb-1">{t('quality.high.badge')}</Badge>
                <CardTitle>{t('quality.high.title')}</CardTitle>
                <CardDescription>{t('quality.high.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.high.feature1')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.high.feature2')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.high.feature3')}</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button className="w-full">
                  {t('quality.high.button')}
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader>
                <Badge className="w-fit mb-1">{t('quality.standard.badge')}</Badge>
                <CardTitle>{t('quality.standard.title')}</CardTitle>
                <CardDescription>{t('quality.standard.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.standard.feature1')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.standard.feature2')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.standard.feature3')}</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button className="w-full">
                  {t('quality.standard.button')}
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader>
                <Badge className="w-fit mb-1">{t('quality.basic.badge')}</Badge>
                <CardTitle>{t('quality.basic.title')}</CardTitle>
                <CardDescription>{t('quality.basic.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.basic.feature1')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.basic.feature2')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.basic.feature3')}</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button className="w-full">
                  {t('quality.basic.button')}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </section>
        
        {/* How It Works */}
        <section className="my-12">
          <h2 className="text-2xl font-bold mb-6 text-center">{t('howItWorks.title')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center mb-2 text-lg font-bold">1</div>
                <CardTitle>{t('howItWorks.step1.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t('howItWorks.step1.description')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center mb-2 text-lg font-bold">2</div>
                <CardTitle>{t('howItWorks.step2.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t('howItWorks.step2.description')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center mb-2 text-lg font-bold">3</div>
                <CardTitle>{t('howItWorks.step3.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t('howItWorks.step3.description')}</p>
              </CardContent>
            </Card>
          </div>
        </section>
        
        {/* Features & Benefits */}
        <section className="my-12">
          <h2 className="text-2xl font-bold mb-6 text-center">{t('benefits.title')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex gap-4">
              <div className="flex-shrink-0 h-10 w-10 bg-muted rounded-lg flex items-center justify-center">
                <Headphones className="h-5 w-5" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">{t('benefits.quality.title')}</h3>
                <p className="text-muted-foreground">
                  {t('benefits.quality.description')} {t('benefits.quality.descriptionExtra')} <a href="https://vdo.tools/en/tools/mp4-to-mp3" target="_blank" rel="noopener" className="text-primary hover:underline">convert MP4 to MP3</a>? Try our specialized tool.
                </p>
              </div>
            </div>
            
            <div className="flex gap-4">
              <div className="flex-shrink-0 h-10 w-10 bg-muted rounded-lg flex items-center justify-center">
                <Download className="h-5 w-5" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">{t('benefits.unlimited.title')}</h3>
                <p className="text-muted-foreground">{t('benefits.unlimited.description')}</p>
              </div>
            </div>
            
            <div className="flex gap-4">
              <div className="flex-shrink-0 h-10 w-10 bg-muted rounded-lg flex items-center justify-center">
                <Zap className="h-5 w-5" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">{t('benefits.speed.title')}</h3>
                <p className="text-muted-foreground">{t('benefits.speed.description')}</p>
              </div>
            </div>
            
            <div className="flex gap-4">
              <div className="flex-shrink-0 h-10 w-10 bg-muted rounded-lg flex items-center justify-center">
                <Shield className="h-5 w-5" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">{t('benefits.privacy.title')}</h3>
                <p className="text-muted-foreground">{t('benefits.privacy.description')}</p>
              </div>
            </div>
          </div>
        </section>
        
        {/* FAQ Section */}
        <section className="my-12">
          <h2 className="text-2xl font-bold mb-6 text-center">{t('faq.title')}</h2>
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('faq.q1.question')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{t('faq.q1.answer')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('faq.q2.question')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{t('faq.q2.answer')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('faq.q3.question')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{t('faq.q3.answer')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('faq.q4.question')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{t('faq.q4.answer')}</p>
              </CardContent>
            </Card>
          </div>
        </section>
        
        {/* CTA Section */}
        <Alert className="my-12">
          <Music className="h-5 w-5" />
          <AlertTitle className="text-lg font-semibold mb-2">{t('cta.title')}</AlertTitle>
          <AlertDescription>
            <p className="mb-4">{t('cta.description')}</p>
            <p className="text-sm text-muted-foreground">{t('cta.disclaimer')}</p>
          </AlertDescription>
        </Alert>
        
      </main>
      <Footer />
    </div>
    </>
  );
}