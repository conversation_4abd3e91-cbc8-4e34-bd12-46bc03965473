import { Metadata } from "next";
import type { Viewport } from "next";
import { Inter } from "next/font/google";
import { NextIntlClientProvider, hasLocale } from 'next-intl';
import {getTranslations} from 'next-intl/server';
import { notFound } from 'next/navigation';
import { routing } from '@/i18n/routing';
import { GoogleAnalytics } from '@next/third-parties/google'
import "../globals.css";

const inter = Inter({ subsets: ["latin"] });

export async function generateMetadata({ params }: { params: { locale: string } }): Promise<Metadata> {
  // Get translations
  const t = await getTranslations();

  return {
    title: {
      template: t('layout.metadata.titleTemplate'),
      default: t('layout.metadata.title'),
    },
    description: t('layout.metadata.description'),
    keywords: t('layout.metadata.keywords'),
    authors: [{ name: t('layout.metadata.author') }],
    creator: t('layout.metadata.creator'),
    publisher: t('layout.metadata.publisher'),
    metadataBase: new URL("https://ytmate.net"),
    openGraph: {
      type: "website",
      locale: params.locale === "en" ? "en_US" : params.locale,
      url: "https://ytmate.net",
      title: t('layout.openGraph.title'),
      description: t('layout.openGraph.description'),
      siteName: t('layout.openGraph.siteName'),
    },
    twitter: {
      card: "summary_large_image",
      title: t('layout.twitter.title'),
      description: t('layout.twitter.description'),
    }
  };
}

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

export default async function RootLayout({
  children,
  params 
}: {
  children: React.ReactNode;
  params: Promise<{locale: string}>;
}) {
  const {locale} = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }
  
  // Get messages for the current locale
 
  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        <meta name="google-site-verification" content="cz11Kh7utJyzjSdgAgs948stwsiOog5pihH88-vWUPo" />
        <meta name="yandex-verification" content="99256c9a226454a8" />
        <meta name="msvalidate.01" content="2252D7F17E09C8404BC6670893DF832C" />
        {process.env.NODE_ENV !== 'development' && <GoogleAnalytics gaId="G-V840W0G67F" />}
        
      </head>
      <body className={inter.className}>
        <NextIntlClientProvider locale={locale}>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}