"use client";

import { useTranslations } from 'next-intl';
import Link from 'next/link';

export function Footer() {
  const t = useTranslations('footer');
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="py-6 mt-6">
      <div className="container mx-auto px-4">
        <div className="border-t border-border pt-4 text-center">
          <div className="flex justify-center flex-wrap gap-x-6 gap-y-2 mb-3">
            <Link href="/terms" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              {t('terms')}
            </Link>
            <Link href="/privacy" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              {t('privacy')}
            </Link>
            <Link href="/dmca" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              {t('dmca')}
            </Link>
          </div>
          <p className="text-sm text-muted-foreground">{t('copyright', { year: currentYear })}</p>
        </div>
      </div>
    </footer>
  );
}