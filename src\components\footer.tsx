"use client";

import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';
import { LANGUAGES } from '@/lib/constants';

export function Footer() {
  const t = useTranslations('footer');
  const currentLocale = useLocale();
  const currentYear = new Date().getFullYear();

  return (
    <footer className="py-6 mt-6">
      <div className="container mx-auto px-4">
        <div className="border-t border-border pt-4">

          {/* Language Links Section */}
          <div className="text-center mb-4">
            <h3 className="text-sm font-medium text-muted-foreground mb-3">{t('languages')}</h3>
            <div className="flex justify-center flex-wrap gap-x-4 gap-y-2">
              {LANGUAGES.map((language) => (
                <Link
                  key={language.value}
                  href={`/${language.value}`}
                  className={`text-sm transition-colors ${
                    currentLocale === language.value
                      ? 'text-foreground font-medium'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                  hrefLang={language.value}
                >
                  {language.label}
                </Link>
              ))}
            </div>
          </div>

          {/* Legal Links Section */}
          <div className="text-center">
            <div className="flex justify-center flex-wrap gap-x-6 gap-y-2 mb-3">
              <Link href="/terms" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                {t('terms')}
              </Link>
              <Link href="/privacy" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                {t('privacy')}
              </Link>
              <Link href="/dmca" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                {t('dmca')}
              </Link>
            </div>
            <p className="text-sm text-muted-foreground">{t('copyright', { year: currentYear })}</p>
          </div>
        </div>
      </div>
    </footer>
  );
}