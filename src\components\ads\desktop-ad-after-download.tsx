"use client";

import Script from "next/script";
import { useEffect, useState } from "react";

export function DesktopAdAfterDownload() {
  const [isProduction, setIsProduction] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);

  useEffect(() => {
    // Check if we're in production environment
    setIsProduction(process.env.NODE_ENV === 'production');
    
    // Check if device is desktop/laptop (screen width >= 1024px)
    const checkIsDesktop = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };
    
    // Initial check
    checkIsDesktop();
    
    // Listen for window resize
    window.addEventListener('resize', checkIsDesktop);
    
    return () => {
      window.removeEventListener('resize', checkIsDesktop);
    };
  }, []);

  // Don't render anything in development or on mobile/tablet
  if (!isProduction || !isDesktop) {
    return null;
  }

  return (
    <div className="w-full flex justify-center py-4 bg-muted/30 hidden lg:flex">
      <div className="ads-container">
        <Script
          id="desktop-ad-after-download-config"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              if (typeof atAsyncOptions !== 'object') var atAsyncOptions = [];
              atAsyncOptions.push({
                  'key': '535a465aa6ce62db43bf914803fa8eb1',
                  'format': 'js',
                  'async': true,
                  'container': 'atContainer-535a465aa6ce62db43bf914803fa8eb1-after-download',
                  'params' : {}
              });
              var script = document.createElement('script');
              script.type = "text/javascript";
              script.async = true;
              script.src = 'http' + (location.protocol === 'https:' ? 's' : '') + '://www.highperformanceformat.com/535a465aa6ce62db43bf914803fa8eb1/invoke.js';
              document.getElementsByTagName('head')[0].appendChild(script);
            `
          }}
        />
        <div id="atContainer-535a465aa6ce62db43bf914803fa8eb1-after-download"></div>
      </div>
    </div>
  );
}
