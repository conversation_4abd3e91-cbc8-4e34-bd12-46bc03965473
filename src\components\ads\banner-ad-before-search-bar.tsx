"use client";

import Script from "next/script";
import { useEffect, useState } from "react";

export function TopBannerAd() {
  const [isProduction, setIsProduction] = useState(false);

  useEffect(() => {
    // Check if we're in production environment
    setIsProduction(process.env.NODE_ENV === 'production');
  }, []);

  // Don't render anything in development
  if (!isProduction) {
    return null;
  }

  return (
    <div className="w-full flex justify-center py-4 bg-muted/30">
      <div className="ads-container">
        <Script
          id="top-banner-ad-config"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              atOptions = {
                'key' : '669dbc15c5b60331750000a56031bd7c',
                'format' : 'iframe',
                'height' : 60,
                'width' : 468,
                'params' : {}
              };
            `
          }}
        />
        <Script
          id="top-banner-ad-invoke"
          src="//www.highperformanceformat.com/669dbc15c5b60331750000a56031bd7c/invoke.js"
          strategy="afterInteractive"
        />
      </div>
    </div>
  );
}