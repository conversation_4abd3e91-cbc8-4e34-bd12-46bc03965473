import { <PERSON><PERSON> } from "@/components/header";
import { Footer } from "@/components/footer";
import { SearchBarWrapper } from "@/components/search-bar-wrapper";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Check, Download, Video, Shield, Zap, Tv, MonitorSmartphone, Smartphone } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from 'next-intl';
import { Metadata } from "next";
import { getTranslations } from 'next-intl/server';
import Script from "next/script";
import { generateMp3PageSchemas } from "@/lib/structured-data";

export async function generateMetadata({ params }: { params: { locale: string } }): Promise<Metadata> {
  // Get translations with namespace
  const t = await getTranslations({ locale: params.locale, namespace: 'ytToMp4.metadata' });

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: t('title'),
      description: t('description'),
    }
  };
}

export default function YouTubeToMP4Page() {
  const t = useTranslations('ytToMp4');
  const layout = useTranslations('layout');
  
  // Extract FAQ items for structured data
  const faqItems = [
    { 
      question: t('faq.q1.question'), 
      answer: t('faq.q1.answer') 
    },
    { 
      question: t('faq.q2.question'), 
      answer: t('faq.q2.answer') 
    },
    { 
      question: t('faq.q3.question'), 
      answer: t('faq.q3.answer') 
    },
    { 
      question: t('faq.q4.question'), 
      answer: t('faq.q4.answer') 
    }
  ];
  
  // Generate structured data for JSON-LD
  const jsonLdData = generateMp3PageSchemas(
    t('metadata.title'),
    t('metadata.description'),
    layout('metadata.publisher'),
    faqItems
  );

  return (
    <>
    <Script
      id="json-ld"
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLdData) }}
      strategy="afterInteractive"
    />
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        {/* Hero Section */}
        <section className="text-center mb-10">
          <h1 className="text-4xl font-bold mb-4">{t('hero.title')}</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto mb-6">
            {t('hero.description')}
          </p>
        </section>
        
        {/* Search Bar */}
        <section className="mb-12">
          <SearchBarWrapper />
        </section>
        
        {/* Quality Options - Higher Resolutions */}
        <section className="my-12">
          <h2 className="text-2xl font-bold mb-6 text-center">{t('quality.premium.title')}</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <Badge className="w-fit mb-1">{t('quality.premium.ultra.badge')}</Badge>
                <CardTitle>{t('quality.premium.ultra.title')}</CardTitle>
                <CardDescription>{t('quality.premium.ultra.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.premium.ultra.feature1')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.premium.ultra.feature2')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.premium.ultra.feature3')}</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button className="w-full">
                  {t('quality.premium.ultra.button')}
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader>
                <Badge className="w-fit mb-1">{t('quality.premium.quad.badge')}</Badge>
                <CardTitle>{t('quality.premium.quad.title')}</CardTitle>
                <CardDescription>{t('quality.premium.quad.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.premium.quad.feature1')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.premium.quad.feature2')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.premium.quad.feature3')}</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button className="w-full">
                  {t('quality.premium.quad.button')}
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader>
                <Badge className="w-fit mb-1">{t('quality.premium.fullhd.badge')}</Badge>
                <CardTitle>{t('quality.premium.fullhd.title')}</CardTitle>
                <CardDescription>{t('quality.premium.fullhd.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.premium.fullhd.feature1')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.premium.fullhd.feature2')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.premium.fullhd.feature3')}</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button className="w-full">
                  {t('quality.premium.fullhd.button')}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </section>
        
        {/* Quality Options - Lower Resolutions */}
        <section className="my-12">
          <h3 className="text-xl font-semibold mb-6 text-center">{t('quality.standard.title')}</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <Badge variant="outline" className="w-fit mb-1">{t('quality.standard.hd.badge')}</Badge>
                <CardTitle>{t('quality.standard.hd.title')}</CardTitle>
                <CardDescription>{t('quality.standard.hd.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.standard.hd.feature1')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.standard.hd.feature2')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.standard.hd.feature3')}</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  {t('quality.standard.hd.button')}
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader>
                <Badge variant="outline" className="w-fit mb-1">{t('quality.standard.sd.badge')}</Badge>
                <CardTitle>{t('quality.standard.sd.title')}</CardTitle>
                <CardDescription>{t('quality.standard.sd.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.standard.sd.feature1')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.standard.sd.feature2')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.standard.sd.feature3')}</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  {t('quality.standard.sd.button')}
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader>
                <Badge variant="outline" className="w-fit mb-1">{t('quality.standard.low.badge')}</Badge>
                <CardTitle>{t('quality.standard.low.title')}</CardTitle>
                <CardDescription>{t('quality.standard.low.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.standard.low.feature1')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.standard.low.feature2')}</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{t('quality.standard.low.feature3')}</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  {t('quality.standard.low.button')}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </section>
        
        {/* How It Works */}
        <section className="my-12">
          <h2 className="text-2xl font-bold mb-6 text-center">{t('howItWorks.title')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center mb-2 text-lg font-bold">1</div>
                <CardTitle>{t('howItWorks.step1.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t('howItWorks.step1.description')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center mb-2 text-lg font-bold">2</div>
                <CardTitle>{t('howItWorks.step2.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t('howItWorks.step2.description')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center mb-2 text-lg font-bold">3</div>
                <CardTitle>{t('howItWorks.step3.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t('howItWorks.step3.description')}</p>
              </CardContent>
            </Card>
          </div>
        </section>
        
        {/* Device Compatibility */}
        <section className="my-12">
          <h2 className="text-2xl font-bold mb-6 text-center">{t('devices.title')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <Smartphone className="h-5 w-5 mb-2" />
                <CardTitle>{t('devices.mobile.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>{t('devices.mobile.device1.name')}</span>
                    <span className="font-medium">{t('devices.mobile.device1.quality')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{t('devices.mobile.device2.name')}</span>
                    <span className="font-medium">{t('devices.mobile.device2.quality')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{t('devices.mobile.device3.name')}</span>
                    <span className="font-medium">{t('devices.mobile.device3.quality')}</span>
                  </div>
                  <Separator className="my-2" />
                  <div className="text-sm font-medium">
                    <strong>{t('devices.recommendationLabel')}</strong> {t('devices.mobile.recommendation')}
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <MonitorSmartphone className="h-5 w-5 mb-2" />
                <CardTitle>{t('devices.computer.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>{t('devices.computer.device1.name')}</span>
                    <span className="font-medium">{t('devices.computer.device1.quality')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{t('devices.computer.device2.name')}</span>
                    <span className="font-medium">{t('devices.computer.device2.quality')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{t('devices.computer.device3.name')}</span>
                    <span className="font-medium">{t('devices.computer.device3.quality')}</span>
                  </div>
                  <Separator className="my-2" />
                  <div className="text-sm font-medium">
                    <strong>{t('devices.recommendationLabel')}</strong> {t('devices.computer.recommendation')}
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <Tv className="h-5 w-5 mb-2" />
                <CardTitle>{t('devices.tv.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>{t('devices.tv.device1.name')}</span>
                    <span className="font-medium">{t('devices.tv.device1.quality')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{t('devices.tv.device2.name')}</span>
                    <span className="font-medium">{t('devices.tv.device2.quality')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{t('devices.tv.device3.name')}</span>
                    <span className="font-medium">{t('devices.tv.device3.quality')}</span>
                  </div>
                  <Separator className="my-2" />
                  <div className="text-sm font-medium">
                    <strong>{t('devices.recommendationLabel')}</strong> {t('devices.tv.recommendation')}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
        
        {/* Features & Benefits */}
        <section className="my-12">
          <h2 className="text-2xl font-bold mb-6 text-center">{t('benefits.title')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex gap-4">
              <div className="flex-shrink-0 h-10 w-10 bg-muted rounded-lg flex items-center justify-center">
                <Zap className="h-5 w-5" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">{t('benefits.conversion.title')}</h3>
                <p className="text-muted-foreground">{t('benefits.conversion.description')}</p>
              </div>
            </div>
            
            <div className="flex gap-4">
              <div className="flex-shrink-0 h-10 w-10 bg-muted rounded-lg flex items-center justify-center">
                <Shield className="h-5 w-5" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">{t('benefits.security.title')}</h3>
                <p className="text-muted-foreground">{t('benefits.security.description')}</p>
              </div>
            </div>
            
            <div className="flex gap-4">
              <div className="flex-shrink-0 h-10 w-10 bg-muted rounded-lg flex items-center justify-center">
                <Download className="h-5 w-5" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">{t('benefits.free.title')}</h3>
                <p className="text-muted-foreground">{t('benefits.free.description')}</p>
              </div>
            </div>
            
            <div className="flex gap-4">
              <div className="flex-shrink-0 h-10 w-10 bg-muted rounded-lg flex items-center justify-center">
                <Video className="h-5 w-5" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">{t('benefits.audio.title')}</h3>
                <p className="text-muted-foreground">
                  {t('benefits.audio.description')} {t('benefits.audio.descriptionExtra')} <a href="https://vdo.tools/en/tools/video-to-audio" target="_blank" rel="noopener" className="text-primary hover:underline">video to audio conversion</a> from other formats, explore our additional tools.
                </p>
              </div>
            </div>
          </div>
        </section>
        
        {/* FAQ Section */}
        <section className="my-12">
          <h2 className="text-2xl font-bold mb-6 text-center">{t('faq.title')}</h2>
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('faq.q1.question')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{t('faq.q1.answer')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('faq.q2.question')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{t('faq.q2.answer')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('faq.q3.question')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{t('faq.q3.answer')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('faq.q4.question')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{t('faq.q4.answer')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('faq.q5.question')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{t('faq.q5.answer')}</p>
              </CardContent>
            </Card>
          </div>
        </section>
        
        {/* CTA Section */}
        <Alert className="my-12">
          <Video className="h-5 w-5" />
          <AlertTitle className="text-lg font-semibold mb-2">{t('cta.title')}</AlertTitle>
          <AlertDescription>
            <p className="mb-4">{t('cta.description')}</p>
            <p className="text-sm text-muted-foreground">{t('cta.disclaimer')}</p>
          </AlertDescription>
        </Alert>
        
      </main>
      <Footer />
    </div>
    </>
  );
}