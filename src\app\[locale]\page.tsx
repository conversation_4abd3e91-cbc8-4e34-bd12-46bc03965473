import { <PERSON><PERSON> } from "@/components/header";
import { Footer } from "@/components/footer";
import Script from "next/script";
import { SearchBarWrapper } from "@/components/search-bar-wrapper";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Check, Headphones, Shield, Zap } from "lucide-react";
import Link from "next/link";
import { useTranslations } from 'next-intl';
import { generateHomePageSchemas } from "@/lib/structured-data";


export default function HomePage() {
  const t = useTranslations('home');
  const layout = useTranslations('layout');
  const jsonLdData = generateHomePageSchemas(
    layout('metadata.title'),
    layout('metadata.description'),
    layout('metadata.publisher')
  );
  return (
    <>
    <Script
      id="json-ld"
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLdData) }}
      strategy="afterInteractive"
    />
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        <section className="text-center mb-10">
          <h1 className="text-4xl font-bold mb-4">{t('hero.title')}</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            {t('hero.description')}
          </p>
        </section>
        
        <section className="mb-12">
          <SearchBarWrapper />
        </section>
        
        <section className="my-12">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('mp3.title')}</CardTitle>
                <CardDescription>{t('mp3.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <div dangerouslySetInnerHTML={{ __html: t('mp3.premium') }} />
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <div dangerouslySetInnerHTML={{ __html: t('mp3.high') }} />
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <div dangerouslySetInnerHTML={{ __html: t('mp3.standard') }} />
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <div dangerouslySetInnerHTML={{ __html: t('mp3.basic') }} />
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button asChild className="w-full">
                  <Link href="/youtube-to-mp3">{t('mp3.button')}</Link>
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('mp4.title')}</CardTitle>
                <CardDescription>{t('mp4.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <div dangerouslySetInnerHTML={{ __html: t('mp4.ultra') }} />
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <div dangerouslySetInnerHTML={{ __html: t('mp4.quad') }} />
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <div dangerouslySetInnerHTML={{ __html: t('mp4.fullhd') }} />
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <div dangerouslySetInnerHTML={{ __html: t('mp4.hd') }} />
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5" />
                    <div dangerouslySetInnerHTML={{ __html: t('mp4.standard') }} />
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button asChild variant="secondary" className="w-full">
                  <Link href="/youtube-to-mp4">{t('mp4.button')}</Link>
                </Button>
              </CardFooter>
            </Card>
          </div>
        </section>
        
        <section id="about" className="my-16">
          <h2 className="text-2xl font-bold mb-6">{t('features.title')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex-shrink-0 h-10 w-10 bg-muted rounded-lg flex items-center justify-center mb-2">
                  <Zap className="h-5 w-5" />
                </div>
                <CardTitle>{t('features.fast.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t('features.fast.description')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <div className="flex-shrink-0 h-10 w-10 bg-muted rounded-lg flex items-center justify-center mb-2">
                  <Headphones className="h-5 w-5" />
                </div>
                <CardTitle>{t('features.premium.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t('features.premium.description')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <div className="flex-shrink-0 h-10 w-10 bg-muted rounded-lg flex items-center justify-center mb-2">
                  <Shield className="h-5 w-5" />
                </div>
                <CardTitle>{t('features.registration.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t('features.registration.description')}</p>
              </CardContent>
            </Card>
          </div>
        </section>
        
        <section id="how-to-use" className="my-16">
          <h2 className="text-2xl font-bold mb-6">{t('howto.title')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="text-center">
              <CardHeader className="pb-2">
                <div className="bg-muted rounded-full w-12 h-12 flex items-center justify-center mx-auto">
                  <span className="font-bold text-lg">1</span>
                </div>
                <CardTitle className="mt-2">{t('howto.step1.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t('howto.step1.description')}</p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader className="pb-2">
                <div className="bg-muted rounded-full w-12 h-12 flex items-center justify-center mx-auto">
                  <span className="font-bold text-lg">2</span>
                </div>
                <CardTitle className="mt-2">{t('howto.step2.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t('howto.step2.description')}</p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader className="pb-2">
                <div className="bg-muted rounded-full w-12 h-12 flex items-center justify-center mx-auto">
                  <span className="font-bold text-lg">3</span>
                </div>
                <CardTitle className="mt-2">{t('howto.step3.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t('howto.step3.description')}</p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader className="pb-2">
                <div className="bg-muted rounded-full w-12 h-12 flex items-center justify-center mx-auto">
                  <span className="font-bold text-lg">4</span>
                </div>
                <CardTitle className="mt-2">{t('howto.step4.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t('howto.step4.description')}</p>
              </CardContent>
            </Card>
          </div>
        </section>
        
        <section id="faq" className="my-16">
          <h2 className="text-2xl font-bold mb-6">{t('faq.title')}</h2>
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('faq.q1.question')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{t('faq.q1.answer')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('faq.q2.question')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{t('faq.q2.answer')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('faq.q3.question')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{t('faq.q3.answer')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('faq.q4.question')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{t('faq.q4.answer')}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('faq.q5.question')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{t('faq.q5.answer')}</p>
              </CardContent>
            </Card>
          </div>
        </section>
        
      </main>
      <Footer />
    </div>
    </>
  );
}