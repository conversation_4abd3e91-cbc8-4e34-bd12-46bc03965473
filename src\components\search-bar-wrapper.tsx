"use client";

import { useState } from "react";
import { SearchBar } from "./search-bar";
import { VideoPreview } from "./video-preview";
import { FormatSelector } from "./format-selector";
import { DownloadButton } from "./download-button";
import { TopBannerAd } from "./ads/top-banner-ad";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import { youtubeClient, VideoInfo, DownloadFormat } from "@/lib/youtube-client";
import { useTranslations } from 'next-intl';

export function SearchBarWrapper() {
  const t = useTranslations('searchWrapper');
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [videoInfo, setVideoInfo] = useState<VideoInfo | null>(null);
  const [selectedFormat, setSelectedFormat] = useState<DownloadFormat | null>(null);

  // Convert API video formats to our app's format structure
  const convertVideoFormats = (info: VideoInfo): DownloadFormat[] => {
    // Filter out duplicate quality levels - keep ones with URLs if possible
    const uniqueFormats = new Map<number, DownloadFormat>();
    
    info.video_formats.forEach(format => {
      // Create our format object
      const downloadFormat: DownloadFormat = {
        quality: format.quality,
        label: t('formats.video', { quality: format.quality }),
        url: format.url,
        container: 'mp4',
        type: 'video',
        height: format.height,
        width: format.width
      };
      
      // If we already have this quality but current one has URL, replace it
      if (uniqueFormats.has(format.quality)) {
        const existing = uniqueFormats.get(format.quality)!;
        if (!existing.url && format.url) {
          uniqueFormats.set(format.quality, downloadFormat);
        }
      } else {
        // Otherwise add this quality
        uniqueFormats.set(format.quality, downloadFormat);
      }
    });
    
    // Convert map to array and sort by quality (high to low)
    return Array.from(uniqueFormats.values())
      .sort((a, b) => b.quality - a.quality);
  };

  // Convert API audio formats to our app's format structure
  const convertAudioFormats = (info: VideoInfo): DownloadFormat[] => {
    return info.audio_formats.map(format => ({
      quality: format.quality,
      label: t('formats.audio', { quality: format.quality }),
      url: format.url,
      container: 'mp3',
      type: 'audio'
    }));
  };

  const handleSearch = async (url: string) => {
    setIsLoading(true);
    setError(null);
    setVideoInfo(null);
    setSelectedFormat(null);

    try {
      const videoData = await youtubeClient.getVideoInfo(url);
      setVideoInfo(videoData);
      
      // Auto-select the default format
      const videoFormats = convertVideoFormats(videoData);
      const audioFormats = convertAudioFormats(videoData);
      
      // Find formats with available URLs
      const availableVideoFormats = videoFormats.filter(f => f.url !== null);
      
      // First try to find the default selected format that has a URL
      const defaultFormat = availableVideoFormats.find(format => 
        format.quality === videoData.default_selected
      );
      
      // If no default format with URL is found, select the first available video format or audio format
      if (defaultFormat) {
        setSelectedFormat(defaultFormat);
      } else if (availableVideoFormats.length > 0) {
        setSelectedFormat(availableVideoFormats[0]);
      } else if (audioFormats.length > 0) {
        setSelectedFormat(audioFormats[0]);
      } else {
        // If no formats have URLs, we'll select the default format anyway 
        // and will fetch the URL when downloading
        const fallbackFormat = videoFormats.find(f => f.quality === videoData.default_selected) || 
                             (videoFormats.length > 0 ? videoFormats[0] : 
                              audioFormats.length > 0 ? audioFormats[0] : null);
        
        setSelectedFormat(fallbackFormat);
      }
      
    } catch (err) {
      console.error('Error fetching video info:', err);
      setError(err instanceof Error ? err.message : t('errorDefault'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormatSelect = (format: DownloadFormat) => {
    setSelectedFormat(format);
  };

  // Split formats into video and audio
  const videoFormats = videoInfo ? convertVideoFormats(videoInfo) : [];
  const audioFormats = videoInfo ? convertAudioFormats(videoInfo) : [];

  return (
    <div className="container mx-auto max-w-3xl px-4 py-8">
      <SearchBar onSearch={handleSearch} isLoading={isLoading} />
      
      {isLoading && (
        <div className="flex justify-center my-12">
          <div className="flex flex-col items-center">
            <Loader2 className="h-10 w-10 animate-spin text-primary" />
            <p className="mt-4 text-muted-foreground">{t('loading')}</p>
          </div>
        </div>
      )}
      
      {error && (
        <Alert variant="destructive" className="my-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {videoInfo && (
        <div className="space-y-6">
          <VideoPreview
            videoId={videoInfo.videoId || ''}
            title={videoInfo.title}
            thumbnailUrl={videoInfo.thumbnail}
            // @ts-ignore
            duration={videoInfo.duration}
            // @ts-ignore
            author={videoInfo.author}
          />
          
          <FormatSelector
            videoFormats={videoFormats}
            audioFormats={audioFormats}
            onFormatSelect={handleFormatSelect}
            selectedFormatId={selectedFormat?.quality || null}
          />
          
          <DownloadButton
            selectedFormat={selectedFormat}
            videoTitle={videoInfo.title}
            videoId={videoInfo.videoId || ''}
          />

          <TopBannerAd />
        </div>
      )}
    </div>
  );
}