"use client";

import { useEffect, useRef, useState } from 'react';

export function DesktopAdBeforeSearch(): JSX.Element | null {
  const banner = useRef<HTMLDivElement>(null);
  const [isProduction, setIsProduction] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);

  const atOptions = {
    key: '535a465aa6ce62db43bf914803fa8eb1',
    format: 'iframe',
    height: 90,
    width: 728,
    params: {},
  };

  useEffect(() => {
    // Check if we're in production environment
    setIsProduction(process.env.NODE_ENV === 'production');

    // Check if device is desktop/laptop (screen width >= 1024px)
    const checkIsDesktop = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };

    // Initial check
    checkIsDesktop();

    // Listen for window resize
    window.addEventListener('resize', checkIsDesktop);

    return () => {
      window.removeEventListener('resize', checkIsDesktop);
    };
  }, []);

  useEffect(() => {
    if (banner.current && !banner.current.firstChild && isProduction && isDesktop) {
      const conf = document.createElement('script');
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = `//www.highperformanceformat.com/${atOptions.key}/invoke.js`;
      conf.innerHTML = `atOptions = ${JSON.stringify(atOptions)}`;

      banner.current.append(conf);
      banner.current.append(script);
    }
  }, [banner, isProduction, isDesktop]);

  // Don't render anything in development or on mobile/tablet
  if (!isProduction || !isDesktop) {
    return null;
  }

  return (
    <div className="w-full flex justify-center py-2 bg-muted/30 hidden lg:flex">
      <div
        className="border border-gray-200 justify-center items-center text-white text-center"
        ref={banner}
      ></div>
    </div>
  );
}
